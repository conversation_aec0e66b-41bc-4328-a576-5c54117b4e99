import { readFile } from 'node:fs/promises';

function parseCSV(csv: string): string[][] {
    return csv.split('\n')
        .filter((line) => line.trim() !== '')
        .map((line) => line.split(';').map((value) => {
            if (value.startsWith('"')) {
                return value.slice(1, -1).replace(/""/g, '"');
            }
            return value;
        }));
}

function prepareIndex(headers: string[]) {
    const index = new Map<string, number>();
    headers.forEach((header, i) => {
        index.set(header, i);
    });
    return index;
}

// "BARTOSZEWICZ Artur"
// "BIEJAT Magdalena Agnieszka"
// "BRAUN Grzegorz Michał"
// "HOŁOWNIA Szymon Franciszek"
// "JAKUBIAK Marek"
// "MACIAK Maciej"
// "MENTZEN Sławomir Jerzy"
// "NAWROCKI Karol <PERSON>"
// "SENYSZYN Joanna"
// "STANOWSKI Krz<PERSON>z<PERSON>"
// "TRZASKOWSKI Rafał Kazimierz"
// "WOCH Marek Marian"
// "ZANDBERG Adrian Tadeusz"

const cNawrocki = 'NAWROCKI Karol Tadeusz';

const cTraskowski = 'TRZASKOWSKI Rafał Kazimierz';

const cBiejat = 'BIEJAT Magdalena Agnieszka';
const cHolownia = 'HOŁOWNIA Szymon Franciszek';
const cSenyszyn = 'SENYSZYN Joanna';
const cZandberg = 'ZANDBERG Adrian Tadeusz';

const cTotalVotes = "Liczba kart ważnych";
const cTotalRound1 = "Liczba głosów ważnych oddanych łącznie na wszystkich kandydatów (z kart ważnych)"
const cTotalRound2 = "Liczba głosów ważnych oddanych łącznie na obu kandydatów (z kart ważnych)"

type Round = {
    location: string;
    // total: number;
    valid: number;
    // invalid: number;
    cNawrocki: number;
    cTraskowski: number;
    right?: number;
    left?: number;
}

type Suspect = {
    round1: Round,
    round2: Round,
    difference: number,
};

function parseRound(row: string[], fields: Map<string, number>): Round {
    const lewica = Number(row[fields.get(cBiejat)] ?? 0) + Number(row[fields.get(cHolownia)] ?? 0) + Number(row[fields.get(cSenyszyn)] ?? 0) + Number(row[fields.get(cTraskowski)] ?? 0)+ Number(row[fields.get(cZandberg)] ?? 0);
    const valid = getTotal(row, fields);
    return {
        location: locationIndex(row),
        valid: valid,
        cNawrocki: Number(row[fields.get(cNawrocki)]),
        cTraskowski: Number(row[fields.get(cTraskowski)]),
        left: lewica,
        right: valid - lewica,
    }
}

function locationIndex(row: string[]): string {
    return [
        row[0], // Nr komisji
        row[1], // Gmina
        row[3], // Powiat
        row[5], // Województwo
        row[6], // Siedziba
        // row[7], // Typ obwodu
        // row[8], // Typ obszaru
    ].join(', ');
}

function getTotal(row: string[], fields: Map<string, number>): number {
    const round1 = Number(row[fields.get(cTotalRound1)] ?? 0);
    const round2 = Number(row[fields.get(cTotalRound2)] ?? 0);
    return round1 ? round1 : round2;
}

async function main() {
    const part1Raw = await readFile('../../part-1.csv', 'utf8');
    const part2Raw = await readFile('../../part-2.csv', 'utf8');

    const part1Cells = parseCSV(part1Raw);
    const part2Cells = parseCSV(part2Raw);

    const part1Headers = part1Cells.shift();
    const part2Headers = part2Cells.shift();

    console.log(`Round 1: ${part1Cells.length}`);
    console.log(`Round 2: ${part2Cells.length}`);

    const p1 = prepareIndex(part1Headers);
    const p2 = prepareIndex(part2Headers);

    const round1 = new Map<string, Round>();
    const round2 = new Map<string, Round>();

    for (const row of part1Cells) {
        const round = parseRound(row, p1);
        round1.set(round.location, round);
    }

    for (const row of part2Cells) {
        const round = parseRound(row, p2);
        round2.set(round.location, round);
    }

    console.log(`Parsed round 1: ${round1.size}`);
    console.log(`Parsed round 2: ${round2.size}`);

    const locations = Array.from(round1.keys());
    console.log(`Checking ${locations.length} locations`);
    let totalDiff = 0;
    const suspected: Suspect[] = [];
    for (const location of locations) {
        const r1 = round1.get(location);
        const r2 = round2.get(location);
        if (!r2) {
            console.log(`Missing round 2 for ${location}`);
            continue;
        }

        // console.log(`${location}: ${r1.cTraskowski} vs ${r2.cTraskowski}`);

        const r1Trzaskowski = r1.cTraskowski > r1.cNawrocki;
        const r2Trzaskowski = r2.cTraskowski > r2.cNawrocki;
        const r1Left = r1.left > r1.right;
        const r2Left = r2.left > r2.right;

        // if (r1Left !== r2Left) {
        if (r1Left === true && r2Left === false) {
            // console.log(`${location}: Different winner ${r1Left} (${r1.left} vs ${r1.right}) -> ${r2Left}`);
            // console.log(`\tNawrocki: ${r1.cNawrocki} -> ${r2.cNawrocki}`);
            // console.log(`\tTrzaskowski: ${r1.cTraskowski} -> ${r2.cTraskowski}`);
            // totalDiff += Math.abs(r2.left - r2.right);
            suspected.push({
                round1: r1,
                round2: r2,
                difference: Math.abs(r2.left - r2.right),
            });
        }

        // if (r1Trzaskowski !== r2Trzaskowski) {
        //     console.log(`${location}: Different winner ${r1Trzaskowski} -> ${r2Trzaskowski}`);
        //     console.log(`\tNawrocki: ${r1.cNawrocki} -> ${r2.cNawrocki}`);
        //     console.log(`\tTrzaskowski: ${r1.cTraskowski} -> ${r2.cTraskowski}`);
        //     console.log(`\tLeft: ${r1.left} -> ${r2.left}`);
        //     console.log(`\tRight: ${r1.right} -> ${r2.right}`);
        // }

        // if (r1.cNawrocki < r2.cNawrocki) {
        //     console.log(`${location}: Nawrocki gained votes ${r1.cNawrocki} -> ${r2.cNawrocki}`);
        //     console.log(`\tTrzaskowski: ${r1.cTraskowski} -> ${r2.cTraskowski}`);
        //     console.log(`\tLeft: ${r1.left} -> ${r2.left}`);
        //     console.log(`\tRight: ${r1.right} -> ${r2.right}`);
        // }
        //
        // if (r1.cNawrocki < r1.cTraskowski && r2.cNawrocki > r2.cTraskowski) {
        //     console.log(`${location}: Trzaskowski area, ${r1.cTraskowski} -> ${r2.cTraskowski}`);
        //     console.log(`\tNawrocki: ${r1.cNawrocki} -> ${r2.cNawrocki}`);
        //     console.log(`\tLeft: ${r1.left} -> ${r2.left}`);
        //     console.log(`\tRight: ${r1.right} -> ${r2.right}`);
        // }

    }

    suspected.sort((a, b) => b.difference - a.difference);
    for (const suspect of suspected) {
        const r1 = suspect.round1;
        const r2 = suspect.round2;
        console.log(`${suspect.round1.location}:`);
        console.log(`\tKoalicja vs prawica, Tura 1: ${r1.left} vs ${r1.right} -> Tura druga: ${r2.left} vs ${r2.right}`);
        console.log(`\tNawrocki: ${r1.cNawrocki} -> ${r2.cNawrocki}`);
        console.log(`\tTrzaskowski: ${r1.cTraskowski} -> ${r2.cTraskowski}`);
        console.log();
        totalDiff+= suspect.difference;
    }

    console.log(`Done (${locations.length})`)
    console.log(`Total vote difference: ${totalDiff}`);

    // Part 1:
    // "Nr komisji"
    // "Gmina"
    // "Teryt Gminy"
    // "Powiat"
    // "Teryt Powiatu"
    // "Województwo"
    // "Siedziba"
    // "Typ obwodu"
    // "Typ obszaru"
    // "Liczba kart do głosowania otrzymanych przez obwodową komisję wyborczą, ustalona po ich przeliczeniu przed rozpoczęciem głosowania z uwzględnieniem ewentualnych kart otrzymanych z rezerwy"
    // "Liczba wyborców uprawnionych do głosowania (umieszczonych w spisie, z uwzględnieniem dodatkowych formularzy) w chwili zakończenia głosowania"
    // "Liczba niewykorzystanych kart do głosowania"
    // "Liczba wyborców, którym wydano karty do głosowania w lokalu wyborczym (liczba podpisów w spisie oraz adnotacje o wydaniu karty bez potwierdzenia podpisem w spisie)"
    // "Liczba wyborców, którym wysłano pakiety wyborcze"
    // "Liczba wyborców, którym wydano karty do głosowania w lokalu wyborczym oraz w głosowaniu korespondencyjnym (łącznie)"
    // "Liczba wyborców głosujących przez pełnomocnika (liczba kart do głosowania wydanych na podstawie aktów pełnomocnictwa otrzymanych przez obwodową komisję wyborczą)"
    // "Liczba wyborców głosujących na podstawie zaświadczenia o prawie do głosowania"
    // "Liczba otrzymanych kopert zwrotnych w głosowaniu korespondencyjnym"
    // "Liczba kopert zwrotnych w głosowaniu korespondencyjnym, w których nie było oświadczenia o osobistym i tajnym oddaniu głosu"
    // "Liczba kopert zwrotnych w głosowaniu korespondencyjnym, w których oświadczenie nie było podpisane przez wyborcę"
    // "Liczba kopert zwrotnych w głosowaniu korespondencyjnym, w których nie było koperty na kartę do głosowania"
    // "Liczba kopert zwrotnych w głosowaniu korespondencyjnym, w których znajdowała się niezaklejona koperta na kartę do głosowania"
    // "Liczba kopert na kartę do głosowania w głosowaniu korespondencyjnym wrzuconych do urny"
    // "Liczba kart wyjętych z urny"
    // "w tym liczba kart wyjętych z kopert na kartę do głosowania w głosowaniu korespondencyjnym"
    // "Liczba kart nieważnych (bez pieczęci obwodowej komisji wyborczej lub inne niż urzędowo ustalone)"
    // "Liczba kart ważnych"
    // "Liczba głosów nieważnych (z kart ważnych)"
    // "w tym z powodu postawienia znaku „X” obok nazwiska dwóch lub większej liczby kandydatów"
    // "w tym z powodu niepostawienia znaku „X” obok nazwiska żadnego kandydata"
    // "w tym z powodu postawienia znaku „X” wyłącznie obok nazwiska skreślonego kandydata"
    // "Liczba głosów ważnych oddanych łącznie na wszystkich kandydatów (z kart ważnych)"
    // "BARTOSZEWICZ Artur"
    // "BIEJAT Magdalena Agnieszka"
    // "BRAUN Grzegorz Michał"
    // "HOŁOWNIA Szymon Franciszek"
    // "JAKUBIAK Marek"
    // "MACIAK Maciej"
    // "MENTZEN Sławomir Jerzy"
    // "NAWROCKI Karol Tadeusz"
    // "SENYSZYN Joanna"
    // "STANOWSKI Krzysztof Jakub"
    // "TRZASKOWSKI Rafał Kazimierz"
    // "WOCH Marek Marian"
    // "ZANDBERG Adrian Tadeusz"

    // Part 2:
    // "Nr komisji"
    // "Gmina"
    // "Teryt Gminy"
    // "Powiat"
    // "Teryt Powiatu"
    // "Województwo"
    // "Siedziba"
    // "Typ obwodu"
    // "Typ obszaru"
    // "Liczba kart do głosowania otrzymanych przez obwodową komisję wyborczą, ustalona po ich przeliczeniu przed rozpoczęciem głosowania z uwzględnieniem ewentualnych kart otrzymanych z rezerwy"
    // "Liczba wyborców uprawnionych do głosowania (umieszczonych w spisie, z uwzględnieniem dodatkowych formularzy) w chwili zakończenia głosowania"
    // "Liczba niewykorzystanych kart do głosowania"
    // "Liczba wyborców, którym wydano karty do głosowania w lokalu wyborczym (liczba podpisów w spisie oraz adnotacje o wydaniu karty bez potwierdzenia podpisem w spisie)"
    // "Liczba wyborców, którym wysłano pakiety wyborcze"
    // "Liczba wyborców, którym wydano karty do głosowania w lokalu wyborczym oraz w głosowaniu korespondencyjnym (łącznie)"
    // "Liczba wyborców głosujących przez pełnomocnika (liczba kart do głosowania wydanych na podstawie aktów pełnomocnictwa otrzymanych przez obwodową komisję wyborczą)"
    // "Liczba wyborców głosujących na podstawie zaświadczenia o prawie do głosowania"
    // "Liczba otrzymanych kopert zwrotnych w głosowaniu korespondencyjnym"
    // "Liczba kopert zwrotnych w głosowaniu korespondencyjnym, w których nie było oświadczenia o osobistym i tajnym oddaniu głosu"
    // "Liczba kopert zwrotnych w głosowaniu korespondencyjnym, w których oświadczenie nie było podpisane przez wyborcę"
    // "Liczba kopert zwrotnych w głosowaniu korespondencyjnym, w których nie było koperty na kartę do głosowania"
    // "Liczba kopert zwrotnych w głosowaniu korespondencyjnym, w których znajdowała się niezaklejona koperta na kartę do głosowania"
    // "Liczba kopert na kartę do głosowania w głosowaniu korespondencyjnym wrzuconych do urny"
    // "Liczba kart wyjętych z urny"
    // "w tym liczba kart wyjętych z kopert na kartę do głosowania w głosowaniu korespondencyjnym"
    // "Liczba kart nieważnych (bez pieczęci obwodowej komisji wyborczej lub inne niż urzędowo ustalone)"
    // "Liczba kart ważnych"
    // "Liczba głosów nieważnych (z kart ważnych)"
    // "w tym z powodu postawienia znaku „X” obok nazwisk obu kandydatów"
    // "w tym z powodu niepostawienia znaku „X” obok nazwiska żadnego kandydata"
    // "Liczba głosów ważnych oddanych łącznie na obu kandydatów (z kart ważnych)"
    // "NAWROCKI Karol Tadeusz"
    // "TRZASKOWSKI Rafał Kazimierz"
}

main();
