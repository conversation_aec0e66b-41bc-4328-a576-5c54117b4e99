import { ExecutionContext, Injectable } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Reflector } from '@nestjs/core';
import { IS_PUBLIC_KEY } from '../decorators/public';
import { AppConfig } from '../../../../config/app-config';

@Injectable()
export class SharedKeyAuthGuard extends AuthGuard('jwt') {
  constructor(private readonly reflector: Reflector, private readonly config: AppConfig) {
    super();
  }

  canActivate(context: ExecutionContext) {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    const authorization = context.switchToHttp().getRequest().headers.authorization ?? '';
    const split = authorization.split(' ');
    if (split[0].toLowerCase() !== 'bearer') {
      return false;
    }

    if (split[1] === this.config.auth.sharedKey) {
      return true;
    }

    return false;
  }
}
