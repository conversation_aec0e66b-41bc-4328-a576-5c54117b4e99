import { ApiProperty } from '@nestjs/swagger';
import { QrcodeEntity, QrcodeType } from '../entities/qrcode.entity';
import { QrcodePayloadDto } from './qrcode-payload.dto';
import { QrcodeStyleDto } from './qrcode-style.dto';

export class QrcodeDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  createdAt: string;

  @ApiProperty()
  updatedAt: string;

  @ApiProperty()
  organizationId: string;

  @ApiProperty()
  folderId: string;

  @ApiProperty({ enum: QrcodeType })
  type: QrcodeType;

  @ApiProperty()
  subtype: string;

  @ApiProperty({ required: false })
  slug?: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  data: QrcodePayloadDto;

  @ApiProperty()
  summary?: string;

  @ApiProperty()
  style: QrcodeStyleDto;

  @ApiProperty()
  totalVisits?: number;
}

export function mapQrcodeDtoFromEntity(qrcode: QrcodeEntity): QrcodeDto {
  return {
    id: qrcode.id,
    createdAt: qrcode.createdAt.toISOString(),
    updatedAt: qrcode.updatedAt.toISOString(),
    organizationId: qrcode.organizationId,
    folderId: qrcode.folderId,
    type: qrcode.type,
    subtype: qrcode.subtype,
    slug: qrcode.slug,
    name: qrcode.name,
    data: qrcode.data,
    summary: qrcode.summary,
    style: qrcode.style,
    totalVisits: qrcode.totalVisits,
  };
}
