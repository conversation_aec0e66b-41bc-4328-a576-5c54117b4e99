import {
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  Index, JoinColumn,
  ManyToOne,
  PrimaryColumn,
  UpdateDateColumn,
} from 'typeorm';
import { QrcodeStyleDto } from '../dto/qrcode-style.dto';
import { QrcodePayloadDto } from '../dto/qrcode-payload.dto';
import { FolderEntity } from '../../core/folders/entities/folder.entity';

export enum QrcodeType {
  Dynamic = 'dynamic',
  Static = 'static',
}

@Entity('qrcodes')
export class QrcodeEntity {
  @PrimaryColumn()
  id: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ name: 'deleted_at' })
  deletedAt: Date;

  @Column({ name: 'organization_id' })
  organizationId: string;

  @Column({ name: 'folder_id' })
  folderId: string;

  @Column({ name: 'type', type: 'varchar' })
  type: QrcodeType;

  @Column({ name: 'subtype', type: 'varchar', nullable: false })
  @Index()
  subtype: string;

  @Column({ name: 'slug', type: 'varchar', unique: true, nullable: true })
  slug?: string;

  @Column()
  name: string;

  @Column({ type: 'jsonb' })
  data: QrcodePayloadDto;

  @Column({ nullable: true })
  summary?: string;

  @Column({ name: 'total_visits', type: 'int8', default: 0 })
  totalVisits: number;

  @Column({ name:'style', type: 'jsonb' })
  style: QrcodeStyleDto;

  @ManyToOne(() => FolderEntity)
  @JoinColumn({ name: 'folder_id' })
  folder: FolderEntity;
}