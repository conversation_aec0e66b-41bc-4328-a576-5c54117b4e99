import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { QrcodeVisitEntity } from './entities/qrcode-visit.entity';
import { UAParser } from 'ua-parser-js';
import * as maxmind from 'maxmind';
import { QrcodeEntity } from './entities/qrcode.entity';

@Injectable()
export class QrcodeVisitsService {
  private geoReader: maxmind.Reader<maxmind.CityResponse> | null = null;

  constructor(
    @InjectRepository(QrcodeVisitEntity)
    private qrcodeVisitsRepository: Repository<QrcodeVisitEntity>,
    @InjectRepository(QrcodeEntity)
    private qrcodeRepository: Repository<QrcodeEntity>,
  ) {
    this.initializeGeoReader();
  }

  private async initializeGeoReader(): Promise<void> {
    try {
      // Initialize MaxMind GeoLite2 City database
      // Note: You'll need to download the GeoLite2-City.mmdb file and place it in apps/api/data/
      // For now, we'll handle the case where the database is not available
      console.log(process.cwd());
      this.geoReader = await maxmind.open('data/geoip/GeoLite2-City.mmdb');
    } catch (error) {
      console.warn('MaxMind GeoLite2 database not found. Geolocation data will not be available.');
      this.geoReader = null;
    }
  }

  async logVisit(qrcodeId: string, ipAddress: string, userAgent: string, acceptLanguage: string): Promise<void> {
    try {
      // Parse user agent
      const parser = new UAParser(userAgent);
      const uaResult = parser.getResult();

      // Parse accept language to get primary language
      const primaryLang = this.extractPrimaryLanguage(acceptLanguage);

      // Get geolocation data
      const geoData = this.getGeolocationData(ipAddress);

      // Create visit record
      const visitRecord = this.qrcodeVisitsRepository.create({
        qrcodeId,
        createdAt: new Date(),
        httpAcceptLanguage: acceptLanguage,
        httpPrimaryLang: primaryLang,
        uaFull: userAgent,
        uaBrowser: uaResult.browser.name ?? null,
        uaBrowserVersion: uaResult.browser.version ?? null,
        uaEngine: uaResult.engine.name ?? null,
        uaEngineVersion: uaResult.engine.version ?? null,
        uaOs: uaResult.os.name ?? null,
        uaOsVersion: uaResult.os.version ?? null,
        uaDeviceModel: uaResult.device.model ?? null,
        uaDeviceType: uaResult.device.type ?? null,
        uaDeviceVendor: uaResult.device.vendor ?? null,
        uaCpuArch: uaResult.cpu.architecture ?? null,
        geoContinent: geoData?.continent?.names?.en ?? null,
        geoContinentCode: geoData?.continent?.code ?? null,
        geoCountry: geoData?.country?.names?.en ?? null,
        geoCountryCode: geoData?.country?.iso_code ?? null,
        geoCountryEu: geoData?.country?.is_in_european_union ?? null,
        geoCity: geoData?.city?.names?.en ?? null,
        geoCityId: geoData?.city?.geoname_id?.toString() ?? null,
        geoTimeZone: geoData?.location?.time_zone ?? null,
        geoPostalCode: geoData?.postal?.code ?? null,
        geoSubCode: geoData?.subdivisions?.[0]?.iso_code ?? null,
        geoSubName: geoData?.subdivisions?.[0]?.names?.en ?? null,
        geoSubNameId: geoData?.subdivisions?.[0]?.geoname_id?.toString() ?? null,
        geoRadius: geoData?.location?.accuracy_radius ?? null,
        geoLat: geoData?.location?.latitude ?? null,
        geoLon: geoData?.location?.longitude ?? null,
      });

      await this.qrcodeVisitsRepository.save(visitRecord);
      await this.qrcodeRepository.increment({ id: qrcodeId }, 'totalVisits', 1);
    } catch (error) {
      console.error('Error logging QR code visit:', error);
      // Don't throw error to avoid breaking the QR code visit flow
    }
  }

  private extractPrimaryLanguage(acceptLanguage: string): string | null {
    if (!acceptLanguage) return null;

    // Parse Accept-Language header (e.g., "en-US,en;q=0.9,es;q=0.8")
    const languages = acceptLanguage.split(',');
    if (languages.length === 0) return null;

    // Get the first language (highest priority)
    const primaryLang = languages[0].trim().split(';')[0].split('-')[0];
    return primaryLang ?? null;
  }

  private getGeolocationData(ipAddress: string): maxmind.CityResponse | null {
    if (!this.geoReader || !ipAddress) {
      return null;
    }

    try {
      return this.geoReader.get(ipAddress);
    } catch (error) {
      console.warn('Error getting geolocation data for IP:', ipAddress, error);
      return null;
    }
  }

  getVisitStats(qrcodeId: string): any {
    // Logic to retrieve visit statistics for a QR code
    return { qrcodeId, visits: 0 }; // Placeholder return value
  }
}
