import { Controller, Get, Post, Body, Patch, Param, Delete, Query, UseGuards } from '@nestjs/common';
import { QrcodesService } from './qrcodes.service';
import { QrcodeVisitsService } from './qrcode-visits.service';
import { CreateQrcodeDto } from './dto/create-qrcode.dto';
import { UpdateQrcodeDto } from './dto/update-qrcode.dto';
import { QrcodeDto, mapQrcodeDtoFromEntity } from './dto/qrcode.dto';
import { ApiResponse, ApiTags } from '@nestjs/swagger';
import { CurrentOrganizationId } from '../core/auth/decorators/current-organization-id';
import { JwtAuthGuard } from '../core/auth/guards/jwt-auth.guard';
import { QrcodeFilterDto } from './dto/qrcode-filter.dto';
import { PaginatedQrcodesDto } from './dto/paginated-qrcodes.dto';
import { SharedKeyAuthGuard } from '../core/auth/guards/shared-key-auth.guard';
import { CurrentUser } from '../core/auth/decorators/current-user';
import { UserEntity } from '../core/users/entities/user.entity';
import { Public } from '../core/auth/decorators/public';

@ApiTags('QR Codes')
@Controller('qrcodes')
@UseGuards(JwtAuthGuard)
export class QrcodesController {
  constructor(
    private readonly qrcodesService: QrcodesService,
    private readonly qrcodeVisitsService: QrcodeVisitsService,
  ) {}

  @Post()
  @ApiResponse({ type: QrcodeDto })
  async create(
    @Body() createQrcodeDto: CreateQrcodeDto,
    @CurrentOrganizationId() organizationId: string,
  ): Promise<QrcodeDto> {
    const qrcode = await this.qrcodesService.create(createQrcodeDto, organizationId);
    return mapQrcodeDtoFromEntity(qrcode);
  }

  @Get()
  @ApiResponse({ type: PaginatedQrcodesDto })
  async findAll(
    @CurrentOrganizationId() organizationId: string,
    @Query() filter: QrcodeFilterDto,
  ): Promise<PaginatedQrcodesDto> {
    const { data, total } = await this.qrcodesService.findAll(organizationId, filter);

    return {
      data: data.map(mapQrcodeDtoFromEntity),
      total,
      page: filter.page ?? 1,
      limit: filter.limit ?? 10,
    };
  }

  @Get(':id')
  @ApiResponse({ type: QrcodeDto })
  async findOne(
    @Param('id') id: string,
    @CurrentUser() user: UserEntity,
    @CurrentOrganizationId() organizationId?: string,
  ): Promise<QrcodeDto> {
    const qrcode = await this.qrcodesService.findOne(id, organizationId);
    return mapQrcodeDtoFromEntity(qrcode);
  }

  @Post('visit/:slug')
  @Public() // Will bypass JWT auth for this endpoint
  @UseGuards(SharedKeyAuthGuard) // Requires shared key for access
  @ApiResponse({ type: QrcodeDto })
  async visit(
    @Param('slug') slug: string,
    @Query('ip') ip: string,
    @Query('ua') ua: string,
    @Query('lang') lang: string,
  ): Promise<QrcodeDto> {
    const qrcode = await this.qrcodesService.findOneBySlug(slug);

    // Log the visit
    await this.qrcodeVisitsService.logVisit(
      qrcode.id,
      ip,
      ua ?? '',
      lang ?? '',
    );

    return mapQrcodeDtoFromEntity(qrcode);
  }

  @Patch(':id')
  @ApiResponse({ type: QrcodeDto })
  async update(
    @Param('id') id: string,
    @Body() updateQrcodeDto: UpdateQrcodeDto,
    @CurrentOrganizationId() organizationId: string,
  ): Promise<QrcodeDto> {
    const qrcode = await this.qrcodesService.update(id, organizationId, updateQrcodeDto);
    return mapQrcodeDtoFromEntity(qrcode);
  }

  @Delete(':id')
  async remove(@Param('id') id: string, @CurrentOrganizationId() organizationId: string): Promise<void> {
    await this.qrcodesService.remove(id, organizationId);
  }
}
