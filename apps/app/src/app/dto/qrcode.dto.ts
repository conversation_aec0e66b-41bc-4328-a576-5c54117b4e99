export enum QrcodeType {
  Dynamic = 'dynamic',
  Static = 'static',
}

export interface QrcodeStyleDto {
  foregroundColor: string;
  backgroundColor: string;
}

export interface QrcodeDto {
  id: string;
  createdAt: string;
  updatedAt: string;
  organizationId: string;
  folderId: string;
  type: QrcodeType;
  subtype?: string;
  slug?: string;
  name: string;
  data: QrcodePayloadDto;
  summary?: string;
  style: QrcodeStyleDto;
  totalVisits?: number;
}

export type QrcodePayloadDto =
  | StaticTextQrcodePayloadDto
  | StaticLinkQrcodePayloadDto
  | DynamicLinkQrcodePayloadDto
  | WifiQrcodePayloadDto
  | StaticVcardQrcodePayloadDto
  | StaticMeCardQrcodePayloadDto;

export interface StaticTextQrcodePayloadDto {
  type: 'text';
  text: string;
}

export interface StaticLinkQrcodePayloadDto {
  type: 'link';
  url: string;
}

export interface DynamicLinkQrcodePayloadDto {
  type: 'dynamic-link';
  url: string;
}

export interface WifiQrcodePayloadDto {
  type: 'wifi';
  ssid: string;
  password?: string;
  hidden: boolean;
  encryption: string;
  eapMethod?: string;
  phase2Auth?: string;
  identity?: string;
  anonymousIdentity?: boolean;
}

export interface AddressDto {
  street?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
}

export interface StaticVcardQrcodePayloadDto {
  type: 'vcard';
  firstName: string;
  lastName: string;
  nickname: string;
  birthday?: string;
  email?: string;
  mobile?: string;
  phone?: string;
  fax?: string;
  company?: string;
  title?: string;
  honorificPrefix?: string;
  address?: AddressDto;
  website?: string;
  note?: string;
}

export interface StaticMeCardQrcodePayloadDto {
  type: 'meCard';
  firstName: string;
  lastName: string;
  phone?: string;
  email?: string;
  birthday?: string;
  nickname?: string;
  address?: AddressDto;
  url?: string;
  note?: string;
}
