<div class="container mx-auto p-4">
  <div class="flex items-center mb-6">
    <a [routerLink]="'/app/qrcodes/create'" class="btn btn-ghost btn-sm mr-2">
      <span class="material-icons">arrow_back</span>
    </a>
    <h1 class="text-2xl font-bold">Create Dynamic Link QR Code</h1>
  </div>

  <div class="card bg-base-200 shadow-xl max-w-2xl mx-auto">
    <div class="card-body">
      <form [formGroup]="form" (ngSubmit)="onSubmit()">
        <!-- Error message -->
        @if (errorMessage) {
          <div class="alert alert-error mb-4">
            <span class="material-icons">error</span>
            <span>{{ errorMessage }}</span>
          </div>
        }

        <!-- Name field -->
        <div class="form-control w-full mb-4">
          <label class="label" for="name">
            <span class="label-text">QR Code Name</span>
          </label>
          <input
            type="text"
            id="name"
            formControlName="name"
            placeholder="Enter a name for your QR code"
            class="input input-bordered w-full"
          />
          @if (form.get('name')?.invalid && (form.get('name')?.dirty || form.get('name')?.touched)) {
            <div class="text-error text-sm mt-1">
              @if (form.get('name')?.errors?.['required']) {
                <span>Name is required</span>
              }
              @if (form.get('name')?.errors?.['maxlength']) {
                <span>Name cannot be longer than 100 characters</span>
              }
            </div>
          }
        </div>

        <!-- Folder field -->
        <div class="form-control w-full mb-4">
          <label class="label" for="folder">
            <span class="label-text">Folder <span class="text-muted">(optional)</span></span>
          </label>
          <select
            id="folder"
            formControlName="folderId"
            class="select select-bordered w-full"
            [disabled]="isLoading"
          >
            <option value="">- No folder -</option>
            @if (isLoading) {
              <option disabled>Loading folders...</option>
            } @else {
              @for (folder of folders; track folder.id) {
                <option [value]="folder.id">{{ folder.name }}</option>
              }
            }
          </select>
        </div>

        <!-- URL field -->
        <div class="form-control w-full mb-6">
          <label class="label" for="url">
            <span class="label-text">URL</span>
          </label>
          <input
            type="url"
            id="url"
            formControlName="url"
            placeholder="https://example.com"
            class="input input-bordered w-full"
          />
          @if (form.get('url')?.invalid && (form.get('url')?.dirty || form.get('url')?.touched)) {
            <div class="text-error text-sm mt-1">
              @if (form.get('url')?.errors?.['required']) {
                <span>URL is required</span>
              }
              @if (form.get('url')?.errors?.['pattern']) {
                <span>Please enter a valid URL (must start with http:// or https://)</span>
              }
              @if (form.get('url')?.errors?.['maxlength']) {
                <span>URL cannot be longer than 1000 characters</span>
              }
            </div>
          }
        </div>

        <!-- Action buttons -->
        <div class="flex justify-end gap-2">
          <button
            type="button"
            class="btn btn-ghost"
            (click)="onCancel()"
            [disabled]="isSubmitting"
          >
            Cancel
          </button>
          <button
            type="submit"
            class="btn btn-primary"
            [disabled]="form.invalid || isSubmitting"
          >
            @if (isSubmitting) {
              <span class="loading loading-spinner loading-xs"></span>
              Creating...
            } @else {
              Create QR Code
            }
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
