<div class="container mx-auto p-4">
  <div class="flex items-center mb-6">
    <a [routerLink]="'/app/qrcodes/create'" class="btn btn-ghost btn-sm mr-2">
      <span class="material-icons">arrow_back</span>
    </a>
    <h1 class="text-2xl font-bold">Create vCard QR Code</h1>
  </div>


  <div class="alert alert-warning mb-4">
    <span class="material-icons">warning</span>
    <span>
      vCard produce very large QR codes and is not suitable for encoding international characters (unless producing even bigger QR codes).<br/>
      When possible, you should use the <a [routerLink]="QrcodesUrls.create.vcard">MeCard QR codes</a> instead.
    </span>
  </div>

  <div class="card bg-base-200 shadow-xl max-w-2xl mx-auto">
    <div class="card-body">
      <form [formGroup]="form" (ngSubmit)="onSubmit()">
        <!-- Error message -->
        @if (errorMessage) {
          <div class="alert alert-error mb-4">
            <span class="material-icons">error</span>
            <span>{{ errorMessage }}</span>
          </div>
        }

        <!-- Name field -->
        <div class="form-control w-full mb-4">
          <label class="label" for="name">
            <span class="label-text">QR Code Name</span>
          </label>
          <input
            type="text"
            id="name"
            formControlName="name"
            placeholder="Enter a name for your QR code"
            class="input input-bordered w-full"
          />
          @if (form.get('name')?.invalid && (form.get('name')?.dirty || form.get('name')?.touched)) {
            <div class="text-error text-sm mt-1">
              @if (form.get('name')?.errors?.['required']) {
                <span>Name is required</span>
              }
              @if (form.get('name')?.errors?.['maxlength']) {
                <span>Name cannot be longer than 100 characters</span>
              }
            </div>
          }
        </div>

        <!-- Folder field -->
        <div class="form-control w-full mb-4">
          <label class="label" for="folder">
            <span class="label-text">Folder <span class="text-muted">(optional)</span></span>
          </label>
          <select
            id="folder"
            formControlName="folderId"
            class="select select-bordered w-full"
            [disabled]="isLoading"
          >
            <option value="">- No folder -</option>
            @if (isLoading) {
              <option disabled>Loading folders...</option>
            } @else {
              @for (folder of folders; track folder.id) {
                <option [value]="folder.id">{{ folder.name }}</option>
              }
            }
          </select>
        </div>

        <!-- Basic Contact Information -->
        <div class="divider">Contact Information</div>

        <!-- Form-level error for name validation -->
        @if (form.errors?.['nameRequired'] && (form.get('firstName')?.dirty || form.get('lastName')?.dirty)) {
          <div class="alert alert-error mb-4">
            <span class="material-icons">error</span>
            <span>At least one of First Name or Last Name is required</span>
          </div>
        }

        <!-- First Name field -->
        <div class="form-control w-full mb-4">
          <label class="label" for="firstName">
            <span class="label-text">First Name <span class="text-muted">(at least first or last name required)</span></span>
          </label>
          <input
            type="text"
            id="firstName"
            formControlName="firstName"
            placeholder="Enter first name"
            class="input input-bordered w-full"
          />
          @if (form.get('firstName')?.invalid && (form.get('firstName')?.dirty || form.get('firstName')?.touched)) {
            <div class="text-error text-sm mt-1">
              @if (form.get('firstName')?.errors?.['maxlength']) {
                <span>First name cannot be longer than 100 characters</span>
              }
            </div>
          }
        </div>

        <!-- Last Name field -->
        <div class="form-control w-full mb-4">
          <label class="label" for="lastName">
            <span class="label-text">Last Name <span class="text-muted">(at least first or last name required)</span></span>
          </label>
          <input
            type="text"
            id="lastName"
            formControlName="lastName"
            placeholder="Enter last name"
            class="input input-bordered w-full"
          />
          @if (form.get('lastName')?.invalid && (form.get('lastName')?.dirty || form.get('lastName')?.touched)) {
            <div class="text-error text-sm mt-1">
              @if (form.get('lastName')?.errors?.['maxlength']) {
                <span>Last name cannot be longer than 100 characters</span>
              }
            </div>
          }
        </div>

        <!-- Nickname field -->
        <div class="form-control w-full mb-4">
          <label class="label" for="nickname">
            <span class="label-text">Nickname <span class="text-muted">(optional)</span></span>
          </label>
          <input
            type="text"
            id="nickname"
            formControlName="nickname"
            placeholder="Enter nickname"
            class="input input-bordered w-full"
          />
          @if (form.get('nickname')?.invalid && (form.get('nickname')?.dirty || form.get('nickname')?.touched)) {
            <div class="text-error text-sm mt-1">
              @if (form.get('nickname')?.errors?.['maxlength']) {
                <span>Nickname cannot be longer than 100 characters</span>
              }
            </div>
          }
        </div>

        <!-- Mobile field -->
        <div class="form-control w-full mb-4">
          <label class="label" for="mobile">
            <span class="label-text">Mobile <span class="text-muted">(optional)</span></span>
          </label>
          <input
            type="tel"
            id="mobile"
            formControlName="mobile"
            placeholder="Enter mobile number"
            class="input input-bordered w-full"
          />
          @if (form.get('mobile')?.invalid && (form.get('mobile')?.dirty || form.get('mobile')?.touched)) {
            <div class="text-error text-sm mt-1">
              @if (form.get('mobile')?.errors?.['maxlength']) {
                <span>Mobile number cannot be longer than 100 characters</span>
              }
            </div>
          }
        </div>

        <!-- Phone field -->
        <div class="form-control w-full mb-4">
          <label class="label" for="phone">
            <span class="label-text">Phone <span class="text-muted">(optional)</span></span>
          </label>
          <input
            type="tel"
            id="phone"
            formControlName="phone"
            placeholder="Enter phone number"
            class="input input-bordered w-full"
          />
          @if (form.get('phone')?.invalid && (form.get('phone')?.dirty || form.get('phone')?.touched)) {
            <div class="text-error text-sm mt-1">
              @if (form.get('phone')?.errors?.['maxlength']) {
                <span>Phone number cannot be longer than 100 characters</span>
              }
            </div>
          }
        </div>

        <!-- Company field -->
        <div class="form-control w-full mb-4">
          <label class="label" for="company">
            <span class="label-text">Company <span class="text-muted">(optional)</span></span>
          </label>
          <input
            type="text"
            id="company"
            formControlName="company"
            placeholder="Enter company name"
            class="input input-bordered w-full"
          />
          @if (form.get('company')?.invalid && (form.get('company')?.dirty || form.get('company')?.touched)) {
            <div class="text-error text-sm mt-1">
              @if (form.get('company')?.errors?.['maxlength']) {
                <span>Company name cannot be longer than 100 characters</span>
              }
            </div>
          }
        </div>

        <!-- Title field -->
        <div class="form-control w-full mb-4">
          <label class="label" for="title">
            <span class="label-text">Job Title <span class="text-muted">(optional)</span></span>
          </label>
          <input
            type="text"
            id="title"
            formControlName="title"
            placeholder="Enter job title"
            class="input input-bordered w-full"
          />
          @if (form.get('title')?.invalid && (form.get('title')?.dirty || form.get('title')?.touched)) {
            <div class="text-error text-sm mt-1">
              @if (form.get('title')?.errors?.['maxlength']) {
                <span>Job title cannot be longer than 100 characters</span>
              }
            </div>
          }
        </div>

        <!-- Website field -->
        <div class="form-control w-full mb-4">
          <label class="label" for="website">
            <span class="label-text">Website <span class="text-muted">(optional)</span></span>
          </label>
          <input
            type="url"
            id="website"
            formControlName="website"
            placeholder="https://example.com"
            class="input input-bordered w-full"
          />
          @if (form.get('website')?.invalid && (form.get('website')?.dirty || form.get('website')?.touched)) {
            <div class="text-error text-sm mt-1">
              @if (form.get('website')?.errors?.['pattern']) {
                <span>Please enter a valid URL (must start with http:// or https://)</span>
              }
              @if (form.get('website')?.errors?.['maxlength']) {
                <span>Website URL cannot be longer than 100 characters</span>
              }
            </div>
          }
        </div>

        <!-- Note field -->
        <div class="form-control w-full mb-4">
          <label class="label" for="note">
            <span class="label-text">Note <span class="text-muted">(optional)</span></span>
          </label>
          <textarea
            id="note"
            formControlName="note"
            placeholder="Enter additional information"
            class="textarea textarea-bordered w-full"
            rows="3"
          ></textarea>
          @if (form.get('note')?.invalid && (form.get('note')?.dirty || form.get('note')?.touched)) {
            <div class="text-error text-sm mt-1">
              @if (form.get('note')?.errors?.['maxlength']) {
                <span>Note cannot be longer than 100 characters</span>
              }
            </div>
          }
        </div>

        <!-- Advanced Fields (Collapsible) -->
        <div class="collapse collapse-arrow p-0">
          <input type="checkbox" [(ngModel)]="showAdvancedFields" [ngModelOptions]="{standalone: true}" />
          <div class="collapse-title font-medium px-0">
            Additional Information
          </div>
          <div class="collapse-content p-0">
            <!-- Birthday field -->
            <div class="form-control w-full mb-4">
              <label class="label" for="birthday">
                <span class="label-text">Birthday <span class="text-muted">(optional)</span></span>
              </label>
              <input
                type="date"
                id="birthday"
                formControlName="birthday"
                class="input input-bordered w-full"
              />
              <div class="text-sm text-muted mt-1">Format: YYYY-MM-DD</div>
              @if (form.get('birthday')?.invalid && (form.get('birthday')?.dirty || form.get('birthday')?.touched)) {
                <div class="text-error text-sm mt-1">
                  @if (form.get('birthday')?.errors?.['pattern']) {
                    <span>Please enter a valid date in YYYY-MM-DD format</span>
                  }
                </div>
              }
            </div>

            <!-- Honorific Prefix field -->
            <div class="form-control w-full mb-4">
              <label class="label" for="honorificPrefix">
                <span class="label-text">Honorific Prefix <span class="text-muted">(optional)</span></span>
              </label>
              <input
                type="text"
                id="honorificPrefix"
                formControlName="honorificPrefix"
                placeholder="Mr., Mrs., Dr., etc."
                class="input input-bordered w-full"
              />
              @if (form.get('honorificPrefix')?.invalid && (form.get('honorificPrefix')?.dirty || form.get('honorificPrefix')?.touched)) {
                <div class="text-error text-sm mt-1">
                  @if (form.get('honorificPrefix')?.errors?.['maxlength']) {
                    <span>Honorific prefix cannot be longer than 20 characters</span>
                  }
                </div>
              }
            </div>

            <!-- Fax field -->
            <div class="form-control w-full mb-4">
              <label class="label" for="fax">
                <span class="label-text">Fax <span class="text-muted">(optional)</span></span>
              </label>
              <input
                type="tel"
                id="fax"
                formControlName="fax"
                placeholder="Enter fax number"
                class="input input-bordered w-full"
              />
              @if (form.get('fax')?.invalid && (form.get('fax')?.dirty || form.get('fax')?.touched)) {
                <div class="text-error text-sm mt-1">
                  @if (form.get('fax')?.errors?.['maxlength']) {
                    <span>Fax number cannot be longer than 100 characters</span>
                  }
                </div>
              }
            </div>

            <!-- Address Fields -->
            <div class="divider">Address</div>

            <!-- Street field -->
            <div class="form-control w-full mb-4">
              <label class="label" for="street">
                <span class="label-text">Street <span class="text-muted">(optional)</span></span>
              </label>
              <input
                type="text"
                id="street"
                formControlName="street"
                placeholder="Enter street address"
                class="input input-bordered w-full"
              />
              @if (form.get('street')?.invalid && (form.get('street')?.dirty || form.get('street')?.touched)) {
                <div class="text-error text-sm mt-1">
                  @if (form.get('street')?.errors?.['maxlength']) {
                    <span>Street cannot be longer than 100 characters</span>
                  }
                </div>
              }
            </div>

            <!-- City field -->
            <div class="form-control w-full mb-4">
              <label class="label" for="city">
                <span class="label-text">City <span class="text-muted">(optional)</span></span>
              </label>
              <input
                type="text"
                id="city"
                formControlName="city"
                placeholder="Enter city"
                class="input input-bordered w-full"
              />
              @if (form.get('city')?.invalid && (form.get('city')?.dirty || form.get('city')?.touched)) {
                <div class="text-error text-sm mt-1">
                  @if (form.get('city')?.errors?.['maxlength']) {
                    <span>City cannot be longer than 100 characters</span>
                  }
                </div>
              }
            </div>

            <!-- State field -->
            <div class="form-control w-full mb-4">
              <label class="label" for="state">
                <span class="label-text">State/Province <span class="text-muted">(optional)</span></span>
              </label>
              <input
                type="text"
                id="state"
                formControlName="state"
                placeholder="Enter state or province"
                class="input input-bordered w-full"
              />
              @if (form.get('state')?.invalid && (form.get('state')?.dirty || form.get('state')?.touched)) {
                <div class="text-error text-sm mt-1">
                  @if (form.get('state')?.errors?.['maxlength']) {
                    <span>State cannot be longer than 100 characters</span>
                  }
                </div>
              }
            </div>

            <!-- Postal Code field -->
            <div class="form-control w-full mb-4">
              <label class="label" for="postalCode">
                <span class="label-text">Postal Code <span class="text-muted">(optional)</span></span>
              </label>
              <input
                type="text"
                id="postalCode"
                formControlName="postalCode"
                placeholder="Enter postal code"
                class="input input-bordered w-full"
              />
              @if (form.get('postalCode')?.invalid && (form.get('postalCode')?.dirty || form.get('postalCode')?.touched)) {
                <div class="text-error text-sm mt-1">
                  @if (form.get('postalCode')?.errors?.['maxlength']) {
                    <span>Postal code cannot be longer than 50 characters</span>
                  }
                </div>
              }
            </div>

            <!-- Country field -->
            <div class="form-control w-full mb-4">
              <label class="label" for="country">
                <span class="label-text">Country <span class="text-muted">(optional)</span></span>
              </label>
              <input
                type="text"
                id="country"
                formControlName="country"
                placeholder="Enter country"
                class="input input-bordered w-full"
              />
              @if (form.get('country')?.invalid && (form.get('country')?.dirty || form.get('country')?.touched)) {
                <div class="text-error text-sm mt-1">
                  @if (form.get('country')?.errors?.['maxlength']) {
                    <span>Country cannot be longer than 50 characters</span>
                  }
                </div>
              }
            </div>
          </div>
        </div>

        <!-- Action buttons -->
        <div class="flex justify-end gap-2">
          <button
            type="button"
            class="btn btn-ghost"
            (click)="onCancel()"
            [disabled]="isSubmitting"
          >
            Cancel
          </button>
          <button
            type="submit"
            class="btn btn-primary"
            [disabled]="form.invalid || isSubmitting"
          >
            @if (isSubmitting) {
              <span class="loading loading-spinner loading-xs"></span>
              Creating...
            } @else {
              Create QR Code
            }
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
