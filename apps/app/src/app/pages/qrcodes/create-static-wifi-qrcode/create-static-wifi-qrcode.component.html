<div class="container mx-auto p-4">
  <div class="flex items-center mb-6">
    <a [routerLink]="'/app/qrcodes/create'" class="btn btn-ghost btn-sm mr-2">
      <span class="material-icons">arrow_back</span>
    </a>
    <h1 class="text-2xl font-bold">Create WiFi QR Code</h1>
  </div>

  <div class="card bg-base-200 shadow-xl max-w-2xl mx-auto">
    <div class="card-body">
      <form [formGroup]="form" (ngSubmit)="onSubmit()">
        <!-- Error message -->
        @if (errorMessage) {
          <div class="alert alert-error mb-4">
            <span class="material-icons">error</span>
            <span>{{ errorMessage }}</span>
          </div>
        }

        <!-- Name field -->
        <div class="form-control w-full mb-4">
          <label class="label" for="name">
            <span class="label-text">QR Code Name</span>
          </label>
          <input
            type="text"
            id="name"
            formControlName="name"
            placeholder="Enter a name for your QR code"
            class="input input-bordered w-full"
          />
          @if (form.get('name')?.invalid && (form.get('name')?.dirty || form.get('name')?.touched)) {
            <div class="text-error text-sm mt-1">
              @if (form.get('name')?.errors?.['required']) {
                <span>Name is required</span>
              }
              @if (form.get('name')?.errors?.['maxlength']) {
                <span>Name cannot be longer than 100 characters</span>
              }
            </div>
          }
        </div>

        <!-- Folder field -->
        <div class="form-control w-full mb-4">
          <label class="label" for="folderId">
            <span class="label-text">Folder (optional)</span>
          </label>
          <select
            id="folderId"
            formControlName="folderId"
            class="select select-bordered w-full"
          >
            <option value="">No folder</option>
            @for (folder of folders; track folder.id) {
              <option [value]="folder.id">{{ folder.name }}</option>
            }
          </select>
        </div>

        <!-- SSID field -->
        <div class="form-control w-full mb-4">
          <label class="label" for="ssid">
            <span class="label-text">Network Name (SSID)</span>
          </label>
          <input
            type="text"
            id="ssid"
            formControlName="ssid"
            placeholder="Enter the WiFi network name"
            class="input input-bordered w-full"
          />
          @if (form.get('ssid')?.invalid && (form.get('ssid')?.dirty || form.get('ssid')?.touched)) {
            <div class="text-error text-sm mt-1">
              @if (form.get('ssid')?.errors?.['required']) {
                <span>Network name is required</span>
              }
              @if (form.get('ssid')?.errors?.['maxlength']) {
                <span>Network name cannot be longer than 32 characters</span>
              }
            </div>
          }
        </div>

        <!-- Encryption field -->
        <div class="form-control w-full mb-4">
          <label class="label" for="encryption">
            <span class="label-text">Security Type</span>
          </label>
          <select
            id="encryption"
            formControlName="encryption"
            class="select select-bordered w-full"
          >
            <option [value]="WifiEncryption.NONE">None</option>
            <option [value]="WifiEncryption.WPA">WPA/WPA2/WPA3 Personal</option>
            <option [value]="WifiEncryption.WPA2_EAP">WPA2 Enterprise</option>
            <option [value]="WifiEncryption.WEP" class="text-warning">WEP (Deprecated)</option>
          </select>
        </div>

        <!-- Password field - shown for WPA and WEP -->
        @if (form.get('encryption')?.value === WifiEncryption.WPA || form.get('encryption')?.value === WifiEncryption.WEP) {
          <div class="form-control w-full mb-4">
            <label class="label" for="password">
              <span class="label-text">Password</span>
            </label>
            <input
              type="password"
              id="password"
              formControlName="password"
              placeholder="Enter the WiFi password"
              class="input input-bordered w-full"
            />
            @if (form.get('password')?.invalid && (form.get('password')?.dirty || form.get('password')?.touched)) {
              <div class="text-error text-sm mt-1">
                @if (form.get('password')?.errors?.['required']) {
                  <span>Password is required</span>
                }
                @if (form.get('password')?.errors?.['maxlength']) {
                  <span>Password cannot be longer than 32 characters</span>
                }
              </div>
            }
          </div>
        }

        <!-- Enterprise authentication fields - shown only for WPA2-EAP -->
        @if (form.get('encryption')?.value === WifiEncryption.WPA2_EAP) {
          <div class="form-control w-full mb-4">
            <label class="label" for="eapMethod">
              <span class="label-text">EAP Method</span>
            </label>
            <select
              id="eapMethod"
              formControlName="eapMethod"
              class="select select-bordered w-full"
            >
              <option [value]="EapMethod.PEAP">PEAP</option>
              <option [value]="EapMethod.TLS">TLS</option>
              <option [value]="EapMethod.TTLS">TTLS</option>
              <option [value]="EapMethod.PWD">PWD</option>
            </select>
            @if (form.get('eapMethod')?.invalid && (form.get('eapMethod')?.dirty || form.get('eapMethod')?.touched)) {
              <div class="text-error text-sm mt-1">
                @if (form.get('eapMethod')?.errors?.['required']) {
                  <span>EAP method is required</span>
                }
              </div>
            }
          </div>

          <div class="form-control w-full mb-4">
            <label class="label" for="phase2Auth">
              <span class="label-text">Phase 2 Authentication</span>
            </label>
            <select
              id="phase2Auth"
              formControlName="phase2Auth"
              class="select select-bordered w-full"
            >
              <option [value]="Phase2Auth.NONE">None</option>
              <option [value]="Phase2Auth.MSCHAPV2">MSCHAPv2</option>
              <option [value]="Phase2Auth.GTC">GTC</option>
            </select>
            @if (form.get('phase2Auth')?.invalid && (form.get('phase2Auth')?.dirty || form.get('phase2Auth')?.touched)) {
              <div class="text-error text-sm mt-1">
                @if (form.get('phase2Auth')?.errors?.['required']) {
                  <span>Phase 2 authentication is required</span>
                }
              </div>
            }
          </div>

          <div class="form-control w-full mb-4">
            <label class="label" for="identity">
              <span class="label-text">Identity</span>
            </label>
            <input
              type="text"
              id="identity"
              formControlName="identity"
              placeholder="Enter your identity"
              class="input input-bordered w-full"
            />
            @if (form.get('identity')?.invalid && (form.get('identity')?.dirty || form.get('identity')?.touched)) {
              <div class="text-error text-sm mt-1">
                @if (form.get('identity')?.errors?.['required']) {
                  <span>Identity is required</span>
                }
                @if (form.get('identity')?.errors?.['maxlength']) {
                  <span>Identity cannot be longer than 100 characters</span>
                }
              </div>
            }
          </div>

          <div class="form-control mb-4">
            <label class="label cursor-pointer justify-start gap-2">
              <input type="checkbox" formControlName="anonymousIdentity" class="checkbox" />
              <span class="label-text">Use Anonymous Identity</span>
            </label>
          </div>
        }

        <!-- Hidden network checkbox -->
        <div class="form-control mb-6">
          <label class="label cursor-pointer justify-start gap-2">
            <input type="checkbox" formControlName="hidden" class="checkbox" />
            <span class="label-text">Hidden Network</span>
          </label>
        </div>

        <!-- Action buttons -->
        <div class="flex justify-end gap-2">
          <button
            type="button"
            class="btn btn-ghost"
            (click)="onCancel()"
            [disabled]="isSubmitting"
          >
            Cancel
          </button>
          <button
            type="submit"
            class="btn btn-primary"
            [disabled]="form.invalid || isSubmitting"
          >
            @if (isSubmitting) {
              <span class="loading loading-spinner loading-xs"></span>
              Creating...
            } @else {
              Create QR Code
            }
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
