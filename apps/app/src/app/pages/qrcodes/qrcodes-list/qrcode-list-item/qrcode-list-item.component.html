<a href="#" class="card card-focus bg-base-200 hover:bg-base-300 transition-colors cursor-pointer"
     (click)="viewQrcode.emit()">
  <div class="flex p-4 flex-col sm:flex-row gap-4 w-full justify-stretch">
    <div class="flex-grow">
      <h2 class="card-title text-lg font-bold truncate">
        <app-qrcode-type-icon
          [type]="qrcode().type!"
          [payloadType]="qrcode().subtype"
        ></app-qrcode-type-icon>
        {{ qrcode().name }}
      </h2>
      <div class="flex flex-col sm:flex-row gap-8 justify-stretch">
        <div class="flex flex-col gap-2 text-sm space-between w-1/2">
          <p class="mt-1">
            Folder:
            @if (folder()) {
              {{folder()?.name}}
            } @else {
              <span class="text-muted">no folder</span>
            }
          </p>
          <p class="text-xs">{{ qrcode().createdAt | date:'short' }}</p>
        </div>

        <div class="w-1/2">
          <p>{{ qrcode().subtype! | qrcodeTypeName }}</p>
          <p class="text-sm truncate">{{ qrcode().summary }}</p>
        </div>
      </div>
    </div>

    <div class="text-right">
      <!-- analytics -->
      @if (qrcode().type === 'dynamic') {
        <p>Analytics</p>
        <p>
          visits<br/>
          <span class="font-bold text-xl">{{ qrcode().totalVisits ?? 0 }}</span>
        </p>
      } @else {
        <p class="text-muted">No analytics for static QR codes</p>
      }
    </div>

    <div class="flex justify-center items-center">
      <!-- QR code preview -->
      <img
        [src]="qrcodeImageUrl()"
        [alt]="qrcode().name + ' QR Code'"
        class="sm:w-24 sm:h-24 object-contain border border-base-300 rounded-md shadow-sm hover:shadow-md"
        loading="lazy"
        fetchpriority="low"
      />
    </div>

    <div class="flex flex-row sm:flex-col items-center justify-center">
      <button class="btn btn-sm btn-ghost" [routerLink]="['/app/qrcodes', qrcode().id]">
        <span class="material-icons">visibility</span>
      </button>
      @if (qrcode().type === 'dynamic') {
        <button class="btn btn-sm btn-ghost" [routerLink]="['/app/qrcodes', qrcode().id, 'edit']">
          <span class="material-icons">edit</span>
        </button>
      } @else {
        <button class="btn btn-sm btn-ghost" disabled [routerLink]="['/app/qrcodes', qrcode().id, 'edit']">
          <span class="material-icons">edit</span>
        </button>
      }

      <button class="btn btn-sm btn-ghost text-error" (click)="deleteQrcode.emit()">
        <span class="material-icons">delete</span>
      </button>
    </div>
  </div>
</a>
