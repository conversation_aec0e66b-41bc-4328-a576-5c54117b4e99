import { Component, computed, input, output } from '@angular/core';
import { FolderDto } from '../../../../services/folder.service';
import { QrcodeDto } from '../../../../dto/qrcode.dto';
import { DatePipe } from '@angular/common';
import { RouterLink } from '@angular/router';
import { environment } from '../../../../../environments/environment';
import { QrcodeTypeIconComponent } from '../../../../shared/components/qrcode-type-icon/qrcode-type-icon.component';
import { QrcodeTypeNamePipe } from '../../../../shared/pipes/qrcode-type-name.pipe';

@Component({
  selector: 'app-qrcode-list-item',
  imports: [
    DatePipe,
    RouterLink,
    QrcodeTypeIconComponent,
    QrcodeTypeNamePipe
  ],
  templateUrl: './qrcode-list-item.component.html',
  styleUrl: './qrcode-list-item.component.scss'
})
export class QrcodeListItemComponent {
  folder = input<FolderDto | null>(null);
  qrcode = input<QrcodeDto>({} as QrcodeDto);

  viewQrcode = output();
  deleteQrcode = output();

  /**
   * Computed property that returns the URL for the QR code image
   */
  qrcodeImageUrl = computed(() => {
    if (!this.qrcode().id) {
      return '';
    }
    return `${environment.apiUrl}/render/qrcode/${this.qrcode().id}`;
  });
}
