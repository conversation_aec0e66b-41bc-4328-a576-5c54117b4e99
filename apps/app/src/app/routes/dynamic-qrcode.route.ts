import { NextFunction, Request, Response } from 'express';
import { AngularNodeAppEngine, writeResponseToNodeResponse } from '@angular/ssr/node';
import { environment } from '../../environments/environment';
import { QrcodeDto, QrcodeType } from '../dto/qrcode.dto';

export async function handleDynamicQRCode(slug: string, req: Request, res: Response, next: NextFunction, angular: AngularNodeAppEngine): Promise<void> {
  console.log(req.headers);
  if (req.method === 'HEAD') {
    console.log('HEAD HEAD!!!!')
    console.log(req.headers);
  } else if (req.method === 'GET') {
    const apiUrl = environment.apiUrl;
    const url = new URL(`${apiUrl}/qrcodes/visit/${slug}`);
    const sharedKey = process.env['SHARED_KEY'];
    console.log(`sharedKey: ${sharedKey}`)

    url.searchParams.append('ip', extractIpAddress(req));
    url.searchParams.append('ua', req.headers['user-agent'] ?? '');
    url.searchParams.append('lang', req.headers['accept-language'] ?? '');

    const response = await fetch(url.toString(), {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${sharedKey}`,
      }
    });

    if (response.ok) {
      const qrcode = await response.json() as QrcodeDto;

      if (qrcode.type === QrcodeType.Dynamic) {
        if (qrcode.data.type === 'dynamic-link') {
          return res.redirect(302, qrcode.data.url);
        }
      }

      angular
        .handle(req, {
          qrcode,
        })
        .then((response) =>
          response ? writeResponseToNodeResponse(response, res) : next(),
        )
        .catch(next);
    } else {
      next(new Error('Not found'));
    }

    return;
  }

  next();
}

function extractIpAddress(request: Request): string {
  // Check for IP address in various headers (for proxy/load balancer scenarios)
  const forwarded = request.headers['x-forwarded-for'] as string;
  const realIp = request.headers['x-real-ip'] as string;
  const clientIp = request.headers['x-client-ip'] as string;

  if (forwarded) {
    // x-forwarded-for can contain multiple IPs, get the first one
    return forwarded.split(',')[0].trim();
  }

  if (realIp) {
    return realIp;
  }

  if (clientIp) {
    return clientIp;
  }

  // Fallback to connection remote address
  return request.connection.remoteAddress || request.socket.remoteAddress || '';
}
