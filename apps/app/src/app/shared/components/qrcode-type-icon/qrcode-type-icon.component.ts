import { Component, computed, input } from '@angular/core';
import { QrcodeType } from '../../../dto/qrcode.dto';
import { NgStyle } from '@angular/common';

@Component({
  selector: 'app-qrcode-type-icon',
  standalone: true,
  imports: [NgStyle],
  template: `<span
    class="material-icons"
    [ngStyle]="{ color: color() || 'var(--color-primary)' }"
  >{{ iconName() }}</span>`,
  styles: `
    :host {
      display: inline-flex;
      width: 24px;
      height: 24px;
      align-items: center;
      justify-content: center;
      line-height: 1;
    }

    span {
      line-height: 1;
    }
  `
})
export class QrcodeTypeIconComponent {
  type = input<QrcodeType | string>('static');
  payloadType = input<string | undefined>(undefined);
  size = input<'sm' | 'md' | 'lg' | 'xl'>('xl');
  color = input<string | null>(null);
  iconName = computed<string>((): string => this.getIconName(this.payloadType()));

  getIconName(type?: string): string {
    // First check payload type
    switch (type) {
      case 'link':
        return 'link';
      case 'text':
        return 'text_fields';
      case 'wifi':
        return 'wifi';
      case 'vcard':
        return 'contact_page';
      case 'meCard':
        return 'person';
      case 'dynamic-link':
        return 'link';
      case 'social':
        return 'campaign';
      case 'game':
        return 'gamepad';
      case 'app':
        return 'apps'
      case 'feedback':
        return 'feedback';
      case 'rating':
        return 'star_rate';
      case 'ab-link':
        return 'compare';
      default:
        // If no specific payload type or unknown, fall back to generic QR code icon
        return 'qr_code';
    }
  }

  sizeClass(): string {
    // Return Tailwind classes for font sizing
    switch (this.size()) {
      case 'sm':
        return 'text-base'; // 16px
      case 'md':
        return 'text-xl';   // 20px
      case 'lg':
        return 'text-2xl';  // 24px
      case 'xl':
        return 'text-3xl';  // 30px
      default:
        return 'text-xl';   // 20px
    }
  }
}
