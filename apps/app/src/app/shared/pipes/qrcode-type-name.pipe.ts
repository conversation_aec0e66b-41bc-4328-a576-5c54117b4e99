import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'qrcodeTypeName',
  standalone: true,
})
export class QrcodeTypeNamePipe implements PipeTransform {

  transform(value: string, ...args: unknown[]): string {
    switch (value) {
      case 'link':
        return 'Static Link';
      case 'text':
        return 'Static Text';
      case 'wifi':
        return 'WiFi Credentials';
      case 'vcard':
        return 'Static vCard';
      case 'meCard':
        return 'Static MeCard';
      case 'dynamic-link':
        return 'Dynamic Link';
      default:
        return 'Unknown';
    }
  }

}
